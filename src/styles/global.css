@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom base styles */
@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply text-gray-900 bg-white;
  }
}

/* Custom component styles */
@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 text-base font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }
  
  .btn-primary {
    @apply bg-primary text-white hover:bg-blue-700 focus:ring-primary;
  }
  
  .btn-outline-primary {
    @apply border-2 border-primary text-primary hover:bg-primary hover:text-white focus:ring-primary;
  }

  /* Enhanced prose styling for About content */
  .prose a {
    @apply text-primary hover:text-blue-700 font-medium transition-colors duration-300;
  }

  .prose a:hover {
    @apply underline;
  }

  .prose p {
    @apply mb-4;
  }

  .prose br + br {
    @apply block mb-4;
  }
  
  .btn-white {
    @apply bg-white text-gray-900 hover:bg-gray-50 focus:ring-gray-500;
  }
  
  .btn-outline-white {
    @apply border-2 border-white text-white hover:bg-white hover:text-gray-900 focus:ring-white;
  }
  
  .section {
    @apply py-16 lg:py-24;
  }
  
  .section-title {
    @apply text-3xl lg:text-4xl xl:text-5xl font-bold leading-tight;
  }
  
  .subtitle {
    @apply text-primary font-semibold text-sm uppercase tracking-wider mb-4;
  }
  
  .container {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-lg overflow-hidden;
  }
  
  .card-border-bottom {
    @apply border-b-4 border-primary;
  }
  
  .hover-bg-primary {
    @apply hover:bg-primary hover:text-white transition-all duration-300;
  }
  
  .bg-gradient-primary {
    @apply bg-gradient-to-r from-primary to-secondary;
  }
  
  .bg-triangles {
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='m36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }
  
  .bg-shape-triangles {
    background-image: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23f8f9fa' fill-opacity='0.8'%3E%3Cpath d='M20 20.5V18H0v-2h20v-2H0v-2h20v-2H0V8h20V6H0V4h20V2H0V0h22v20h-2z'/%3E%3C/g%3E%3C/svg%3E");
  }
  
  .text-color {
    @apply text-gray-600;
  }
  
  .transition {
    @apply transition-all duration-300;
  }
  
  .navigation {
    @apply bg-white shadow-lg;
  }

  .navigation.fixed-top {
    @apply fixed top-0 left-0 right-0 z-50;
  }
  
  .navbar-brand img {
    @apply h-10 w-auto;
  }
  
  .nav-link {
    @apply px-3 py-2 text-sm font-medium transition-colors duration-300;
  }
  
  .dropdown-menu {
    @apply absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg min-w-48 z-50;
  }
  
  .dropdown-item {
    @apply block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-200;
  }
}

/* Utility classes */
@layer utilities {
  .bg-cover {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
  }
  
  .watermark {
    @apply absolute -top-4 -right-4 text-6xl font-bold opacity-10 pointer-events-none;
  }
  
  .icon-quote {
    @apply text-4xl text-primary;
  }
  
  .partner-logo {
    @apply max-h-12 w-auto object-contain filter grayscale hover:grayscale-0 transition-all duration-300;
  }
}

/* Animation utilities */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

.animate-zoom-in {
  animation: zoomIn 0.6s ease-out;
}
