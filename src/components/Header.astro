---
const { lang = 'en' } = Astro.props;

// Navigation menu items
const menuItems = {
  en: [
    { name: 'Home', url: '/' },
    { name: 'About', url: '/about' },
    { name: 'Services', url: '/services' },
    { name: 'Team', url: '/team' },
    // { name: 'Pricing', url: '/pricing' },
    { name: 'FAQ', url: '/faq' },
    { name: 'Contact', url: '/contact' },
  ],
  fr: [
    { name: 'Accueil', url: '/fr' },
    { name: 'À propos', url: '/fr/about' },
    { name: 'Services', url: '/fr/services' },
    { name: 'Équipe', url: '/fr/team' },
    // { name: 'Tarifs', url: '/fr/pricing' },
    { name: 'FAQ', url: '/fr/faq' },
    { name: 'Contact', url: '/fr/contact' },
  ]
};

const currentMenu = menuItems[lang as keyof typeof menuItems] || menuItems.en;
const homeText = lang === 'fr' ? 'Accueil' : 'Home';
const contactText = lang === 'fr' ? 'Contactez-nous' : 'Contact Us';

// Get current path for language switching
const currentPath = Astro.url.pathname;

// Function to get the equivalent URL in the other language
function getLanguageSwitchUrl(currentPath: string, currentLang: string): string {
  if (currentLang === 'fr') {
    // Switch from French to English
    if (currentPath === '/fr' || currentPath === '/fr/') {
      return '/';
    }
    // Remove /fr prefix for other pages
    return currentPath.replace(/^\/fr/, '') || '/';
  } else {
    // Switch from English to French
    if (currentPath === '/' || currentPath === '') {
      return '/fr';
    }
    // Add /fr prefix for other pages
    return `/fr${currentPath}`;
  }
}

const languageSwitchUrl = getLanguageSwitchUrl(currentPath, lang);
---

<!-- Navigation -->
<div class="navigation fixed top-0 left-0 right-0 z-50 bg-gradient-to-r from-[#FE3634] to-[#FF5958] backdrop-blur-md shadow-lg transition-all duration-300 border-b border-red-200/30" id="navbar">
  <div class="container">
    <nav class="flex items-center justify-between py-4">
      <!-- Logo with hover effect -->
      <a class="flex items-center group" href={lang === 'fr' ? '/fr' : '/'}>
        <img
          src="/images/ptbl-logo.png"
          alt="PTBL | Enabling Connectivity"
          class="h-10 w-auto transition-transform duration-300 group-hover:scale-105"
        >
      </a>

      <!-- Mobile menu button with animated hamburger -->
      <button
        class="lg:hidden relative p-3 text-white hover:text-red-100 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white/20 rounded-lg z-50 bg-white/10 hover:bg-white/20"
        type="button"
        aria-label="Toggle navigation"
        id="navbar-toggler"
      >
        <div class="hamburger-lines">
          <span class="line line1"></span>
          <span class="line line2"></span>
          <span class="line line3"></span>
        </div>
      </button>

      <!-- Desktop Navigation -->
      <div class="hidden lg:flex items-center justify-between flex-1 ml-8" id="navigation">
        <ul class="flex items-center space-x-1">
          <li>
            <a
              class="nav-link relative px-4 py-2 text-white hover:text-red-100 transition-all duration-300 rounded-lg hover:bg-white/10 group"
              href={lang === 'fr' ? '/fr' : '/'}
              data-page="home"
            >
              {homeText}
              <span class="absolute bottom-0 left-1/2 w-0 h-0.5 bg-gradient-to-r from-white to-red-100 transition-all duration-300 group-hover:w-full group-hover:left-0"></span>
            </a>
          </li>
          {currentMenu.slice(1).map((item) => (
            <li>
              <a
                class="nav-link relative px-4 py-2 text-white hover:text-red-100 transition-all duration-300 rounded-lg hover:bg-white/10 group"
                href={item.url}
                data-page={item.name.toLowerCase()}
              >
                {item.name}
                <span class="absolute bottom-0 left-1/2 w-0 h-0.5 bg-gradient-to-r from-white to-red-100 transition-all duration-300 group-hover:w-full group-hover:left-0"></span>
              </a>
            </li>
          ))}
        </ul>

        <!-- Language Switcher & Contact -->
        <div class="flex items-center space-x-4">
          <!-- Enhanced Language Switcher -->
          <div class="relative">
            <a
              href={languageSwitchUrl}
              class="flex items-center px-3 py-2 text-sm font-medium border-2 border-white/30 text-white rounded-lg hover:border-white hover:bg-white hover:text-red-600 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-white/20 group"
            >
              <svg class="w-4 h-4 mr-1.5 transition-transform duration-300 group-hover:scale-110" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clip-rule="evenodd"></path>
              </svg>
              {lang === 'en' ? 'Fr' : 'En'}
            </a>
          </div>

          <!-- Enhanced Contact Button -->
          <a
            href={lang === 'fr' ? '/fr/contact' : '/contact'}
            class="btn btn-white text-red-600 hover:bg-red-50 text-sm relative overflow-hidden group border-2 border-white"
          >
            <span class="relative z-10 flex items-center font-semibold">
              <svg class="w-4 h-4 mr-2 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              {contactText}
            </span>
            <div class="absolute inset-0 bg-gradient-to-r from-white to-red-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </a>
        </div>
      </div>

    </nav>
  </div>
</div>

<!-- Enhanced Mobile Menu -->
<div class="mobile-menu fixed inset-0 z-[9999] lg:hidden" id="mobile-menu">
  <!-- Backdrop -->
  <div class="mobile-backdrop fixed inset-0 bg-black/50 backdrop-blur-sm opacity-0 transition-opacity duration-300 z-[9998]"></div>

  <!-- Menu Panel -->
  <div class="mobile-panel fixed top-0 right-0 h-screen w-80 max-w-[85vw] bg-white shadow-2xl transform translate-x-full transition-transform duration-300 z-[9999]">
    <div class="flex flex-col h-full">
      <!-- Header -->
      <div class="flex items-center justify-between p-6 border-b border-gray-100 bg-gradient-to-r from-[#FE3634] to-[#FF5958]">
        <img src="/images/ptbl-logo.png" alt="PTBL" class="h-8 w-auto">
        <button
          id="close-mobile-menu"
          class="p-2 text-white hover:text-gray-200 hover:bg-white/10 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-white/20"
          aria-label="Close menu"
        >
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>

      <!-- Navigation -->
      <nav class="flex-1 px-6 py-8 overflow-y-auto">
        <ul class="space-y-2">
          {currentMenu.map((item, index) => (
            <li class="mobile-nav-item" style={`animation-delay: ${index * 50}ms`}>
              <a
                href={item.url}
                class="mobile-nav-link flex items-center py-3 px-4 text-lg font-medium text-gray-900 hover:text-[#FE3634] hover:bg-[#FE3634]/5 rounded-lg transition-all duration-300 group"
              >
                <span class="flex-1">{item.name}</span>
                <svg class="w-5 h-5 opacity-0 group-hover:opacity-100 transform translate-x-0 group-hover:translate-x-1 transition-all duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
              </a>
            </li>
          ))}
        </ul>

        <!-- Mobile Actions -->
        <div class="mt-8 pt-8 border-t border-gray-100 space-y-4">
          <!-- Language Switcher - More Prominent -->
          <a
            href={languageSwitchUrl}
            class="flex items-center justify-center w-full bg-gradient-to-r from-[#FE3634] to-[#FF5958] text-white rounded-lg px-4 py-4 font-semibold hover:from-[#FF5958] hover:to-[#FE3634] transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-[#FE3634]/20 group shadow-lg"
          >
            <svg class="w-5 h-5 mr-2 transition-transform duration-300 group-hover:scale-110" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M7 2a1 1 0 011 1v1h3a1 1 0 110 2H9.578a18.87 18.87 0 01-1.724 4.78c.29.354.596.696.914 1.026a1 1 0 11-1.44 1.389c-.188-.196-.373-.396-.554-.6a19.098 19.098 0 01-3.107 3.567 1 1 0 01-1.334-1.49 17.087 17.087 0 003.13-3.733 18.992 18.992 0 01-1.487-2.494 1 1 0 111.79-.89c.234.47.489.928.764 1.372.417-.934.752-1.913.997-2.927H3a1 1 0 110-2h3V3a1 1 0 011-1zm6 6a1 1 0 01.894.553l2.991 5.982a.869.869 0 01.02.037l.99 1.98a1 1 0 11-1.79.895L15.383 16h-4.764l-.724 1.447a1 1 0 11-1.788-.894l.99-1.98.019-.038 2.99-5.982A1 1 0 0113 8zm-1.382 6h2.764L13 11.236 11.618 14z" clip-rule="evenodd"></path>
            </svg>
            🌐 {lang === 'en' ? 'Français' : 'English'}
          </a>

          <!-- Contact Button -->
          <a
            href={lang === 'fr' ? '/fr/contact' : '/contact'}
            class="btn w-full relative overflow-hidden group bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 border-2 border-gray-300 hover:from-[#FE3634] hover:to-[#FF5958] hover:text-white hover:border-transparent transition-all duration-300"
          >
            <span class="relative z-10 flex items-center justify-center">
              <svg class="w-5 h-5 mr-2 transition-transform duration-300 group-hover:scale-110" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              {contactText}
            </span>
            <div class="absolute inset-0 bg-gradient-to-r from-[#FF5958] to-[#FE3634] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          </a>
        </div>
      </nav>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    const navbar = document.getElementById('navbar');
    const toggler = document.getElementById('navbar-toggler');
    const mobileMenu = document.getElementById('mobile-menu');
    const mobilePanel = mobileMenu?.querySelector('.mobile-panel');
    const mobileBackdrop = mobileMenu?.querySelector('.mobile-backdrop');
    const closeBtn = document.getElementById('close-mobile-menu');
    const hamburgerLines = toggler?.querySelectorAll('.line');

    // Debug mobile menu elements
    console.log('Mobile menu elements found:', {
      toggler: !!toggler,
      mobileMenu: !!mobileMenu,
      closeBtn: !!closeBtn,
      mobileBackdrop: !!mobileBackdrop,
      mobilePanel: !!mobilePanel,
      hamburgerLines: hamburgerLines?.length || 0
    });

    // Navbar scroll effect
    let lastScrollY = window.scrollY;

    function updateNavbar() {
      const currentScrollY = window.scrollY;

      if (navbar) {
        if (currentScrollY > 100) {
          navbar.classList.add('navbar-scrolled');
        } else {
          navbar.classList.remove('navbar-scrolled');
        }

        // Hide/show navbar on scroll
        if (currentScrollY > lastScrollY && currentScrollY > 200) {
          navbar.style.transform = 'translateY(-100%)';
        } else {
          navbar.style.transform = 'translateY(0)';
        }
      }

      lastScrollY = currentScrollY;
    }

    window.addEventListener('scroll', updateNavbar, { passive: true });

    // Mobile menu functions
    function openMobileMenu() {
      console.log('Opening mobile menu...');
      if (mobileMenu && mobilePanel && mobileBackdrop) {
        console.log('Mobile menu elements found, proceeding...');
        document.body.style.overflow = 'hidden';

        // Force display the menu
        mobileMenu.classList.add('show');

        // Animate backdrop and panel
        requestAnimationFrame(() => {
          mobileBackdrop.classList.remove('opacity-0');
          mobileBackdrop.classList.add('opacity-100');
          mobilePanel.classList.remove('translate-x-full');
        });

        // Animate hamburger to X
        if (hamburgerLines) {
          (hamburgerLines[0] as HTMLElement).style.transform = 'rotate(45deg) translate(5px, 5px)';
          (hamburgerLines[1] as HTMLElement).style.opacity = '0';
          (hamburgerLines[2] as HTMLElement).style.transform = 'rotate(-45deg) translate(7px, -6px)';
        }

        // Animate menu items
        const menuItems = mobileMenu.querySelectorAll('.mobile-nav-item');
        menuItems.forEach((item, index) => {
          setTimeout(() => {
            (item as HTMLElement).style.opacity = '1';
            (item as HTMLElement).style.transform = 'translateX(0)';
          }, index * 50);
        });
      } else {
        console.error('Mobile menu elements not found:', { mobileMenu, mobilePanel, mobileBackdrop });
      }
    }

    function closeMobileMenu() {
      console.log('Closing mobile menu...');
      if (mobileMenu && mobilePanel && mobileBackdrop) {
        document.body.style.overflow = '';

        // Animate out
        mobileBackdrop.classList.remove('opacity-100');
        mobileBackdrop.classList.add('opacity-0');
        mobilePanel.classList.add('translate-x-full');

        // Reset hamburger
        if (hamburgerLines) {
          (hamburgerLines[0] as HTMLElement).style.transform = '';
          (hamburgerLines[1] as HTMLElement).style.opacity = '';
          (hamburgerLines[2] as HTMLElement).style.transform = '';
        }

        // Hide menu after animation
        setTimeout(() => {
          mobileMenu.classList.remove('show');

          // Reset menu items
          const menuItems = mobileMenu.querySelectorAll('.mobile-nav-item');
          menuItems.forEach(item => {
            (item as HTMLElement).style.opacity = '';
            (item as HTMLElement).style.transform = '';
          });
        }, 300);
      }
    }

    // Event listeners with fallback
    if (toggler) {
      toggler.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Mobile menu toggle clicked');
        openMobileMenu();
      });
    } else {
      console.error('Mobile menu toggler not found!');
    }

    if (closeBtn) {
      closeBtn.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        closeMobileMenu();
      });
    }

    // Close on backdrop click
    if (mobileBackdrop) {
      mobileBackdrop.addEventListener('click', function(e) {
        if (e.target === mobileBackdrop) {
          closeMobileMenu();
        }
      });
    }

    // Fallback: Add click listener to any element with mobile menu trigger class
    document.addEventListener('click', function(e) {
      const target = e.target as HTMLElement;
      if (target && (target.id === 'navbar-toggler' || target.closest('#navbar-toggler'))) {
        e.preventDefault();
        e.stopPropagation();
        console.log('Fallback mobile menu trigger activated');
        openMobileMenu();
      }
    });

    // Close on escape key
    document.addEventListener('keydown', function(e) {
      if (e.key === 'Escape' && mobileMenu && !mobilePanel?.classList.contains('translate-x-full')) {
        closeMobileMenu();
      }
    });

    // Active link highlighting
    function updateActiveLinks() {
      const currentPath = window.location.pathname;
      const navLinks = document.querySelectorAll('.nav-link');

      navLinks.forEach(link => {
        const href = link.getAttribute('href');
        if (href === currentPath || (currentPath === '/' && href === '/') ||
            (currentPath.startsWith('/fr') && href === '/fr' && currentPath === '/fr')) {
          link.classList.add('active');
        } else {
          link.classList.remove('active');
        }
      });
    }

    updateActiveLinks();

    // Smooth scroll for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
      anchor.addEventListener('click', function (this: HTMLAnchorElement, e: Event) {
        e.preventDefault();
        const href = this.getAttribute('href');
        const target = href ? document.querySelector(href) : null;
        if (target) {
          target.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
          });
          closeMobileMenu();
        }
      });
    });
  });
</script>

<style>
  /* Enhanced Navigation Styles */
  .navigation {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .navigation.navbar-scrolled {
    background: linear-gradient(to right, rgba(254, 54, 52, 0.95), rgba(255, 89, 88, 0.95));
    backdrop-filter: blur(16px);
    box-shadow: 0 10px 25px -5px rgba(254, 54, 52, 0.2), 0 10px 10px -5px rgba(254, 54, 52, 0.1);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Animated Hamburger */
  .hamburger-lines {
    @apply w-6 h-5 flex flex-col justify-between;
  }

  .line {
    @apply w-full h-0.5 bg-current transition-all duration-300 ease-in-out;
    transform-origin: center;
  }

  /* Active Navigation Link */
  .nav-link.active {
    @apply text-white;
    background: rgba(255, 255, 255, 0.15);
  }

  .nav-link.active span {
    @apply w-full left-0;
    background: linear-gradient(to right, white, rgba(255, 255, 255, 0.8));
  }

  /* Mobile Menu Enhancements */
  .mobile-menu {
    display: none;
    z-index: 9999;
  }

  .mobile-menu.show {
    display: block;
  }

  .mobile-backdrop {
    backdrop-filter: blur(8px);
    z-index: 9998;
  }

  .mobile-panel {
    box-shadow: -10px 0 25px -5px rgba(0, 0, 0, 0.1), -10px 0 10px -5px rgba(0, 0, 0, 0.04);
    z-index: 9999;
  }

  /* Ensure mobile menu is visible on mobile devices */
  @media (max-width: 1023px) {
    .mobile-menu.show {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  .mobile-nav-item {
    opacity: 0;
    transform: translateX(20px);
    transition: all 0.3s ease-out;
  }

  .mobile-nav-link:hover {
    transform: translateX(4px);
  }

  /* Gradient Button Hover Effects */
  .btn {
    position: relative;
    overflow: hidden;
  }

  .btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .btn:hover::before {
    left: 100%;
  }

  /* Scroll Animations */
  @keyframes slideDown {
    from {
      transform: translateY(-100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }

  @keyframes fadeInScale {
    from {
      opacity: 0;
      transform: scale(0.95);
    }
    to {
      opacity: 1;
      transform: scale(1);
    }
  }

  .mobile-panel {
    animation: slideInRight 0.3s ease-out;
  }

  @keyframes slideInRight {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }

  /* Focus States */
  .nav-link:focus,
  .mobile-nav-link:focus {
    @apply outline-none ring-2 ring-primary/20 ring-offset-2;
  }

  /* Responsive Improvements */
  @media (max-width: 1024px) {
    .container {
      @apply px-4;
    }
  }

  /* Loading Animation for Logo */
  .navigation img {
    animation: fadeInScale 0.6s ease-out;
  }

  /* Hover Effects for Language Switcher */
  .navigation a[href*="language"] {
    position: relative;
    overflow: hidden;
  }

  .navigation a[href*="language"]::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(41, 98, 255, 0.1), transparent);
    transition: left 0.3s ease;
  }

  .navigation a[href*="language"]:hover::after {
    left: 100%;
  }
</style>
