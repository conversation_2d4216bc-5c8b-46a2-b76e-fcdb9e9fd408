---
export interface Props {
  enable: boolean;
  board: {
    title: string;
    members: Array<{
      name: string;
      position: string;
      image: string;
      bio: string;
    }>;
  };
  management: {
    title: string;
    members: Array<{
      name: string;
      position: string;
      image: string;
      bio: string;
    }>;
  };
}

const { enable, board, management } = Astro.props;
---

{enable && (
  <section class="section">
    <div class="container">
      <!-- Board of Directors -->
      <div class="mb-20">
        <h2 class="section-title text-center mb-16" data-aos="fade-up">{board.title}</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {board.members.map((member, index) => (
            <div class="card overflow-hidden hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay={200 * (index + 1)}>
              <div class="aspect-w-4 aspect-h-5">
                <img src={member.image} alt={member.name} class="w-full h-64 object-cover">
              </div>
              <div class="p-6">
                <h4 class="text-xl font-semibold mb-2">{member.name}</h4>
                <p class="text-primary font-medium mb-4">{member.position}</p>
                <p class="text-color text-sm leading-relaxed line-clamp-4">{member.bio}</p>
                <button 
                  class="mt-4 text-primary hover:underline text-sm font-medium"
                  onclick={`openModal('${member.name.replace(/\s+/g, '')}')`}
                >
                  Read More
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <!-- Management Team -->
      <div>
        <h2 class="section-title text-center mb-16" data-aos="fade-up">{management.title}</h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {management.members.map((member, index) => (
            <div class="card overflow-hidden hover:shadow-xl transition-all duration-300" data-aos="fade-up" data-aos-delay={200 * (index + 1)}>
              <div class="aspect-w-4 aspect-h-5">
                <img src={member.image} alt={member.name} class="w-full h-64 object-cover">
              </div>
              <div class="p-6">
                <h4 class="text-xl font-semibold mb-2">{member.name}</h4>
                <p class="text-primary font-medium mb-4">{member.position}</p>
                <p class="text-color text-sm leading-relaxed line-clamp-4">{member.bio}</p>
                <button 
                  class="mt-4 text-primary hover:underline text-sm font-medium"
                  onclick={`openModal('${member.name.replace(/\s+/g, '')}')`}
                >
                  Read More
                </button>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    <!-- Modals for full bios -->
    {[...board.members, ...management.members].map((member) => (
      <div id={`modal-${member.name.replace(/\s+/g, '')}`} class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full max-h-[80vh] overflow-y-auto">
          <div class="p-6">
            <div class="flex items-start gap-6 mb-6">
              <img src={member.image} alt={member.name} class="w-24 h-24 object-cover rounded-lg">
              <div>
                <h3 class="text-2xl font-bold mb-2">{member.name}</h3>
                <p class="text-primary font-medium">{member.position}</p>
              </div>
              <button 
                onclick={`closeModal('${member.name.replace(/\s+/g, '')}')`}
                class="ml-auto text-gray-500 hover:text-gray-700"
              >
                <i class="fas fa-times text-xl"></i>
              </button>
            </div>
            <div class="text-color leading-relaxed whitespace-pre-line">{member.bio}</div>
          </div>
        </div>
      </div>
    ))}
  </section>
)}

<script>
  function openModal(memberName) {
    const modal = document.getElementById(`modal-${memberName}`);
    if (modal) {
      modal.classList.remove('hidden');
      document.body.style.overflow = 'hidden';
    }
  }
  
  function closeModal(memberName) {
    const modal = document.getElementById(`modal-${memberName}`);
    if (modal) {
      modal.classList.add('hidden');
      document.body.style.overflow = 'auto';
    }
  }
  
  // Close modal on escape key
  document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
      const openModal = document.querySelector('.fixed:not(.hidden)');
      if (openModal) {
        openModal.classList.add('hidden');
        document.body.style.overflow = 'auto';
      }
    }
  });
</script>

<style>
  .line-clamp-4 {
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
</style>
