---
export interface Props {
  enable: boolean;
  items: Array<{
    title: string;
    content: string;
  }>;
}

const { enable, items } = Astro.props;
---

{enable && (
  <section class="section bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="container">
      <!-- Section Header -->
      <div class="text-center mb-16">
        <h2 class="text-4xl lg:text-5xl font-bold text-gray-900 mb-4" data-aos="fade-up">
          Our Foundation
        </h2>
        <p class="text-xl text-gray-600 max-w-3xl mx-auto" data-aos="fade-up" data-aos-delay="200">
          Guided by our vision and driven by our mission, we're building the future of connectivity in Ghana
        </p>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-12">
        {items.map((item, index) => (
          <div class="group relative" data-aos="fade-up" data-aos-delay={200 * (index + 1)}>
            <!-- Background decoration -->
            <div class="absolute inset-0 bg-gradient-to-br from-primary/5 to-blue-500/5 rounded-3xl transform rotate-1 group-hover:rotate-2 transition-transform duration-300"></div>

            <!-- Main card -->
            <div class="relative bg-white rounded-3xl p-10 shadow-xl hover:shadow-2xl transition-all duration-500 transform group-hover:-translate-y-2">
              <!-- Icon based on index -->
              <div class="mb-8">
                {index === 0 ? (
                  <!-- Vision icon -->
                  <div class="w-16 h-16 bg-gradient-to-br from-primary to-blue-600 rounded-2xl flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                    </svg>
                  </div>
                ) : (
                  <!-- Mission icon -->
                  <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-emerald-600 rounded-2xl flex items-center justify-center">
                    <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                  </div>
                )}
              </div>

              <h3 class="text-3xl font-bold mb-6 text-gray-900 group-hover:text-primary transition-colors duration-300">
                {item.title}
              </h3>
              <p class="text-gray-600 text-lg leading-relaxed">
                {item.content}
              </p>

              <!-- Decorative element -->
              <div class="absolute top-6 right-6 w-20 h-20 bg-gradient-to-br from-primary/10 to-blue-500/10 rounded-full blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  </section>
)}
