---
export interface Props {
  enable: boolean;
  subtitle: string;
  title: string;
  service_item: Array<{
    title: string;
    icon: string;
    icon_color: string;
    content: string;
  }>;
}

const { enable, subtitle, title, service_item } = Astro.props;

// Color mapping for consistent theming
const colorMap = {
  blue: { bg: 'bg-blue-50', hover: 'group-hover:bg-blue-500', text: 'text-blue-500', gradient: 'from-blue-400 to-blue-600' },
  green: { bg: 'bg-green-50', hover: 'group-hover:bg-green-500', text: 'text-green-500', gradient: 'from-green-400 to-green-600' },
  purple: { bg: 'bg-purple-50', hover: 'group-hover:bg-purple-500', text: 'text-purple-500', gradient: 'from-purple-400 to-purple-600' },
  orange: { bg: 'bg-orange-50', hover: 'group-hover:bg-orange-500', text: 'text-orange-500', gradient: 'from-orange-400 to-orange-600' },
  red: { bg: 'bg-red-50', hover: 'group-hover:bg-red-500', text: 'text-red-500', gradient: 'from-red-400 to-red-600' },
  indigo: { bg: 'bg-indigo-50', hover: 'group-hover:bg-indigo-500', text: 'text-indigo-500', gradient: 'from-indigo-400 to-indigo-600' },
  primary: { bg: 'bg-primary/10', hover: 'group-hover:bg-primary', text: 'text-primary', gradient: 'from-primary to-secondary' },
  yellow: { bg: 'bg-yellow-50', hover: 'group-hover:bg-yellow-500', text: 'text-yellow-600', gradient: 'from-yellow-400 to-yellow-600' }
};
---

{enable && (
  <section class="section relative overflow-hidden">
    <!-- Background decorative elements -->
    <div class="absolute inset-0 bg-gradient-to-br from-gray-50/50 to-white"></div>
    <div class="absolute top-0 left-0 w-72 h-72 bg-primary/5 rounded-full blur-3xl -translate-x-1/2 -translate-y-1/2"></div>
    <div class="absolute bottom-0 right-0 w-96 h-96 bg-secondary/5 rounded-full blur-3xl translate-x-1/2 translate-y-1/2"></div>

    <div class="container relative z-10">
      <!-- Enhanced Header Section -->
      <div class="text-center mb-20">
        <div class="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-secondary rounded-full mb-6" data-aos="zoom-in">
          <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
          </svg>
        </div>
        <p class="subtitle" data-aos="fade-up">{subtitle}</p>
        <h2 class="section-title max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="200">{title}</h2>
        <div class="w-24 h-1 bg-gradient-to-r from-primary to-secondary mx-auto mt-6" data-aos="fade-up" data-aos-delay="400"></div>
      </div>

      <!-- Enhanced Services Grid -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 lg:gap-10">
        {service_item.map((item, index) => {
          const colors = colorMap[item.icon_color as keyof typeof colorMap] || colorMap.primary;
          return (
            <div
              class="service-card group relative bg-white rounded-2xl p-8 text-center hover:shadow-2xl hover:-translate-y-2 transition-all duration-500 border border-gray-100 hover:border-transparent overflow-hidden"
              data-aos="fade-up"
              data-aos-delay={150 * (index + 1)}
            >
              <!-- Card background gradient overlay -->
              <div class={`absolute inset-0 bg-gradient-to-br ${colors.gradient} opacity-0 group-hover:opacity-5 transition-opacity duration-500`}></div>

              <!-- Floating decorative element -->
              <div class="absolute -top-4 -right-4 w-24 h-24 bg-gradient-to-br from-gray-100 to-gray-200 rounded-full opacity-20 group-hover:opacity-30 transition-opacity duration-500"></div>

              <!-- Enhanced Icon Container -->
              <div class="relative z-10 mb-8">
                <div class={`w-24 h-24 mx-auto rounded-2xl ${colors.bg} ${colors.hover} flex items-center justify-center transition-all duration-500 group-hover:scale-110 group-hover:rotate-3 shadow-lg group-hover:shadow-xl`}>
                  <i class={`${item.icon} text-4xl ${colors.text} group-hover:text-white transition-all duration-500`}></i>
                </div>

                <!-- Pulse animation ring -->
                <div class={`absolute inset-0 w-24 h-24 mx-auto rounded-2xl ${colors.bg} opacity-0 group-hover:opacity-30 scale-100 group-hover:scale-125 transition-all duration-700`}></div>
              </div>

              <!-- Content -->
              <div class="relative z-10">
                <h4 class="text-xl font-bold mb-4 text-gray-900 group-hover:text-gray-800 transition-colors duration-300">
                  {item.title}
                </h4>
                <p class="text-gray-600 leading-relaxed mb-6 group-hover:text-gray-700 transition-colors duration-300" set:html={item.content}></p>

                <!-- Learn More Button -->
                <button class={`inline-flex items-center text-sm font-semibold ${colors.text} group-hover:text-white transition-all duration-300 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0`}>
                  Learn More
                  <svg class="w-4 h-4 ml-2 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                  </svg>
                </button>
              </div>

              <!-- Service number badge -->
              <div class="absolute top-4 left-4 w-8 h-8 bg-gray-100 group-hover:bg-white rounded-full flex items-center justify-center text-sm font-bold text-gray-400 group-hover:text-gray-600 transition-all duration-300">
                {String(index + 1).padStart(2, '0')}
              </div>
            </div>
          );
        })}
      </div>

      <!-- Call to Action Section -->
      <div class="text-center mt-16" data-aos="fade-up" data-aos-delay="600">
        <div class="inline-flex items-center justify-center space-x-4 bg-white rounded-2xl p-6 shadow-lg border border-gray-100">
          <div class="flex items-center space-x-2">
            <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
            <span class="text-sm font-medium text-gray-600">Available 24/7</span>
          </div>
          <div class="w-px h-6 bg-gray-200"></div>
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"></path>
            </svg>
            <span class="text-sm font-medium text-gray-600">Expert Support</span>
          </div>
        </div>
      </div>
    </div>
  </section>
)}

<style>
  /* Enhanced Service Card Styles */
  .service-card {
    position: relative;
    backdrop-filter: blur(10px);
  }

  .service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0));
    border-radius: 1rem;
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  .service-card:hover::before {
    opacity: 1;
  }

  /* Floating animation for decorative elements */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    50% {
      transform: translateY(-10px) rotate(2deg);
    }
  }

  .service-card:hover .absolute.-top-4.-right-4 {
    animation: float 3s ease-in-out infinite;
  }

  /* Icon container enhancements */
  .service-card .w-24.h-24 {
    position: relative;
    overflow: hidden;
  }

  .service-card .w-24.h-24::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
  }

  .service-card:hover .w-24.h-24::after {
    width: 100px;
    height: 100px;
  }

  /* Staggered hover effects */
  .service-card:hover {
    transform: translateY(-8px) scale(1.02);
  }

  /* Enhanced button hover effects */
  .service-card button {
    position: relative;
    overflow: hidden;
  }

  .service-card button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
  }

  .service-card:hover button::before {
    left: 100%;
  }

  /* Pulse animation for status indicator */
  @keyframes pulse-glow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(34, 197, 94, 0.4);
    }
    50% {
      box-shadow: 0 0 20px rgba(34, 197, 94, 0.8);
    }
  }

  .animate-pulse {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  /* Responsive enhancements */
  @media (max-width: 768px) {
    .service-card {
      padding: 1.5rem;
    }

    .service-card .w-24.h-24 {
      width: 4rem;
      height: 4rem;
    }

    .service-card i {
      font-size: 1.5rem !important;
    }
  }

  /* Loading state animation */
  @keyframes shimmer {
    0% {
      background-position: -200px 0;
    }
    100% {
      background-position: calc(200px + 100%) 0;
    }
  }

  .service-card.loading {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Add intersection observer for enhanced animations
    const serviceCards = document.querySelectorAll('.service-card');

    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach((entry) => {
        if (entry.isIntersecting) {
          entry.target.classList.add('animate-in');

          // Add staggered animation to child elements
          const icon = entry.target.querySelector('.w-24.h-24');
          const title = entry.target.querySelector('h4');
          const content = entry.target.querySelector('p');
          const button = entry.target.querySelector('button');

          if (icon) {
            setTimeout(() => icon.classList.add('animate-bounce'), 200);
          }
          if (title) {
            setTimeout(() => title.classList.add('animate-fade-in'), 400);
          }
          if (content) {
            setTimeout(() => content.classList.add('animate-fade-in'), 600);
          }
          if (button) {
            setTimeout(() => button.classList.add('animate-fade-in'), 800);
          }
        }
      });
    }, observerOptions);

    serviceCards.forEach(card => {
      observer.observe(card);

      // Add click handler for service cards
      card.addEventListener('click', function() {
        // Add a subtle click animation
        this.style.transform = 'scale(0.98)';
        setTimeout(() => {
          this.style.transform = '';
        }, 150);

        // You can add navigation or modal opening logic here
        console.log('Service clicked:', this.querySelector('h4')?.textContent);
      });

      // Add keyboard accessibility
      card.setAttribute('tabindex', '0');
      card.setAttribute('role', 'button');
      card.setAttribute('aria-label', `Learn more about ${card.querySelector('h4')?.textContent}`);

      card.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.click();
        }
      });
    });

    // Add parallax effect to background elements
    window.addEventListener('scroll', () => {
      const scrolled = window.pageYOffset;
      const parallaxElements = document.querySelectorAll('.absolute.bg-primary\\/5, .absolute.bg-secondary\\/5');

      parallaxElements.forEach((element, index) => {
        const speed = 0.5 + (index * 0.2);
        element.style.transform = `translateY(${scrolled * speed}px)`;
      });
    }, { passive: true });
  });
</script>
