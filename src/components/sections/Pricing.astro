---
export interface Props {
  enable: boolean;
  subtitle?: string;
  title?: string;
  pricing_table: Array<{
    title: string;
    price: string;
    unit: string;
    description: string;
    services: string[];
    link: string;
    featured?: boolean;
  }>;
}

const { enable, subtitle, title, pricing_table } = Astro.props;
---

{enable && (
  <section class="section bg-gray-50">
    <div class="container">
      {(subtitle || title) && (
        <div class="text-center mb-16">
          {subtitle && <p class="subtitle" data-aos="fade-up">{subtitle}</p>}
          {title && <h2 class="section-title" data-aos="fade-up" data-aos-delay="200">{title}</h2>}
        </div>
      )}
      
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
        {pricing_table.map((plan, index) => (
          <div class={`card p-8 text-center relative hover:shadow-xl transition-all duration-300 ${plan.featured ? 'ring-2 ring-primary transform scale-105' : ''}`} data-aos="fade-up" data-aos-delay={200 * (index + 1)}>
            {plan.featured && (
              <div class="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <span class="bg-primary text-white px-4 py-2 rounded-full text-sm font-medium">
                  Most Popular
                </span>
              </div>
            )}
            
            <div class="mb-6">
              <span class="inline-block bg-primary bg-opacity-10 text-primary px-4 py-2 rounded-full text-sm font-medium mb-4">
                {plan.title}
              </span>
              <div class="text-4xl font-bold text-gray-900 mb-2">
                {plan.price}
                <span class="text-lg font-normal text-gray-500">/ {plan.unit}</span>
              </div>
              <p class="text-color">{plan.description}</p>
            </div>
            
            <hr class="my-6">
            
            <ul class="space-y-3 mb-8">
              {plan.services.map((service) => (
                <li class="flex items-center justify-center">
                  <i class="fas fa-check text-green-500 mr-3"></i>
                  <span class="text-color">{service}</span>
                </li>
              ))}
            </ul>
            
            <a 
              href={plan.link} 
              class={`btn w-full ${plan.featured ? 'btn-primary' : 'btn-outline-primary'}`}
            >
              Select Plan
            </a>
          </div>
        ))}
      </div>
      
      <div class="text-center mt-12">
        <p class="text-color text-lg mb-6">
          Need a custom solution? We offer tailored packages for enterprise clients.
        </p>
        <a href="/contact" class="btn btn-outline-primary">
          Contact for Custom Quote
        </a>
      </div>
    </div>
  </section>
)}
