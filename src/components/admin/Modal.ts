// Modal utility for admin interface
export class Modal {
  private modal: HTMLElement | null = null;
  private backdrop: HTMLElement | null = null;

  constructor(private containerId: string = 'modalContainer') {}

  show(title: string, content: string, options: {
    size?: 'sm' | 'md' | 'lg' | 'xl';
    closable?: boolean;
    onClose?: () => void;
  } = {}) {
    const { size = 'md', closable = true, onClose } = options;
    
    this.hide(); // Close any existing modal
    
    const container = document.getElementById(this.containerId);
    if (!container) return;

    // Create backdrop
    this.backdrop = document.createElement('div');
    this.backdrop.className = 'fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4';
    
    // Create modal
    this.modal = document.createElement('div');
    this.modal.className = `bg-white rounded-lg shadow-xl max-h-full overflow-hidden ${this.getSizeClass(size)}`;
    
    this.modal.innerHTML = `
      <div class="flex justify-between items-center px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">${title}</h3>
        ${closable ? `
          <button class="modal-close text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        ` : ''}
      </div>
      <div class="modal-content overflow-y-auto max-h-96">
        ${content}
      </div>
    `;

    this.backdrop.appendChild(this.modal);
    container.appendChild(this.backdrop);

    // Add event listeners
    if (closable) {
      this.backdrop.addEventListener('click', (e) => {
        if (e.target === this.backdrop) {
          this.hide();
          onClose?.();
        }
      });

      const closeButton = this.modal.querySelector('.modal-close');
      closeButton?.addEventListener('click', () => {
        this.hide();
        onClose?.();
      });

      // ESC key
      document.addEventListener('keydown', this.handleEscKey);
    }

    // Prevent body scroll
    document.body.style.overflow = 'hidden';
  }

  hide() {
    if (this.backdrop) {
      this.backdrop.remove();
      this.backdrop = null;
    }
    this.modal = null;
    
    // Restore body scroll
    document.body.style.overflow = '';
    
    // Remove ESC listener
    document.removeEventListener('keydown', this.handleEscKey);
  }

  private handleEscKey = (e: KeyboardEvent) => {
    if (e.key === 'Escape') {
      this.hide();
    }
  };

  private getSizeClass(size: string): string {
    switch (size) {
      case 'sm': return 'w-full max-w-md';
      case 'md': return 'w-full max-w-lg';
      case 'lg': return 'w-full max-w-2xl';
      case 'xl': return 'w-full max-w-4xl';
      default: return 'w-full max-w-lg';
    }
  }

  getModal(): HTMLElement | null {
    return this.modal;
  }
}

// Form builder utility
export class FormBuilder {
  private fields: Array<{
    type: string;
    name: string;
    label: string;
    value?: any;
    required?: boolean;
    options?: Array<{value: string, label: string}>;
    placeholder?: string;
    rows?: number;
  }> = [];

  addField(field: {
    type: 'text' | 'email' | 'password' | 'textarea' | 'select' | 'checkbox' | 'file' | 'hidden';
    name: string;
    label: string;
    value?: any;
    required?: boolean;
    options?: Array<{value: string, label: string}>;
    placeholder?: string;
    rows?: number;
  }) {
    this.fields.push(field);
    return this;
  }

  build(submitLabel: string = 'Save', onSubmit?: (data: FormData) => void): string {
    const formId = `form_${Date.now()}`;
    
    const fieldsHTML = this.fields.map(field => {
      switch (field.type) {
        case 'hidden':
          return `<input type="hidden" name="${field.name}" value="${field.value || ''}" />`;
          
        case 'textarea':
          return `
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">${field.label}</label>
              <textarea 
                name="${field.name}" 
                rows="${field.rows || 3}"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                placeholder="${field.placeholder || ''}"
                ${field.required ? 'required' : ''}
              >${field.value || ''}</textarea>
            </div>
          `;
          
        case 'select':
          return `
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">${field.label}</label>
              <select 
                name="${field.name}" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                ${field.required ? 'required' : ''}
              >
                ${field.options?.map(option => 
                  `<option value="${option.value}" ${option.value === field.value ? 'selected' : ''}>${option.label}</option>`
                ).join('') || ''}
              </select>
            </div>
          `;
          
        case 'checkbox':
          return `
            <div class="mb-4">
              <label class="flex items-center">
                <input 
                  type="checkbox" 
                  name="${field.name}" 
                  value="true"
                  class="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                  ${field.value ? 'checked' : ''}
                />
                <span class="ml-2 text-sm text-gray-700">${field.label}</span>
              </label>
            </div>
          `;
          
        case 'file':
          return `
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">${field.label}</label>
              <input 
                type="file" 
                name="${field.name}" 
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                ${field.required ? 'required' : ''}
              />
            </div>
          `;
          
        default: // text, email, password
          return `
            <div class="mb-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">${field.label}</label>
              <input 
                type="${field.type}" 
                name="${field.name}" 
                value="${field.value || ''}"
                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-primary focus:border-primary"
                placeholder="${field.placeholder || ''}"
                ${field.required ? 'required' : ''}
              />
            </div>
          `;
      }
    }).join('');

    const formHTML = `
      <form id="${formId}" class="p-6">
        ${fieldsHTML}
        <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200">
          <button 
            type="button" 
            class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
            onclick="document.querySelector('.modal-close')?.click()"
          >
            Cancel
          </button>
          <button 
            type="submit" 
            class="px-4 py-2 text-sm font-medium text-white bg-primary border border-transparent rounded-md hover:bg-primary/90"
          >
            ${submitLabel}
          </button>
        </div>
      </form>
    `;

    // Add submit handler after form is rendered
    setTimeout(() => {
      const form = document.getElementById(formId);
      if (form && onSubmit) {
        form.addEventListener('submit', (e) => {
          e.preventDefault();
          const formData = new FormData(form as HTMLFormElement);
          onSubmit(formData);
        });
      }
    }, 100);

    return formHTML;
  }
}

// Notification utility
export class Notification {
  static show(message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info', duration: number = 5000) {
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white shadow-lg rounded-lg pointer-events-auto ring-1 ring-black ring-opacity-5 overflow-hidden transform transition-all duration-300 translate-x-full`;
    
    const colors = {
      success: 'text-green-800 bg-green-50 border-green-200',
      error: 'text-red-800 bg-red-50 border-red-200',
      warning: 'text-yellow-800 bg-yellow-50 border-yellow-200',
      info: 'text-blue-800 bg-blue-50 border-blue-200'
    };

    const icons = {
      success: '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>',
      error: '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>',
      warning: '<path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>',
      info: '<path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>'
    };

    notification.innerHTML = `
      <div class="p-4 ${colors[type]} border">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
              ${icons[type]}
            </svg>
          </div>
          <div class="ml-3">
            <p class="text-sm font-medium">${message}</p>
          </div>
          <div class="ml-auto pl-3">
            <div class="-mx-1.5 -my-1.5">
              <button class="inline-flex rounded-md p-1.5 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-green-50 focus:ring-green-600">
                <svg class="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                  <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    `;

    document.body.appendChild(notification);

    // Animate in
    setTimeout(() => {
      notification.classList.remove('translate-x-full');
    }, 100);

    // Auto remove
    setTimeout(() => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        notification.remove();
      }, 300);
    }, duration);

    // Manual close
    const closeButton = notification.querySelector('button');
    closeButton?.addEventListener('click', () => {
      notification.classList.add('translate-x-full');
      setTimeout(() => {
        notification.remove();
      }, 300);
    });
  }
}
