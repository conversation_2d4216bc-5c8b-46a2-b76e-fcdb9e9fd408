// Server-side content fetching for Astro components
// This runs on the server during build/SSR

interface ContentSection {
  id?: string;
  section_key: string;
  language: 'en' | 'fr';
  title?: string;
  subtitle?: string;
  content?: string;
  data?: Record<string, unknown>;
  enabled?: boolean;
  sort_order?: number;
}

interface SiteImage {
  id?: string;
  image_key: string;
  alt_text?: string | null;
  file: string;
  description?: string | null;
  category?: string;
  active?: boolean;
}

// Server-side PocketBase client
class ServerPocketBase {
  private baseUrl: string;

  constructor() {
    this.baseUrl = import.meta.env.PUBLIC_POCKETBASE_URL || 'http://127.0.0.1:8090';
  }

  async getContentSection(sectionKey: string, language: 'en' | 'fr' = 'en'): Promise<ContentSection | null> {
    try {
      const url = `${this.baseUrl}/api/collections/content_sections/records?filter=section_key="${sectionKey}" && language="${language}"&perPage=1`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.items && data.items.length > 0) {
        return data.items[0] as ContentSection;
      }
      
      return null;
    } catch (error) {
      console.warn(`Failed to fetch content section ${sectionKey} (${language}):`, error);
      return null;
    }
  }

  async getImage(imageKey: string): Promise<SiteImage | null> {
    try {
      const url = `${this.baseUrl}/api/collections/site_images/records?filter=image_key="${imageKey}" && active=true&perPage=1`;
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      const data = await response.json();
      
      if (data.items && data.items.length > 0) {
        return data.items[0] as SiteImage;
      }
      
      return null;
    } catch (error) {
      console.warn(`Failed to fetch image ${imageKey}:`, error);
      return null;
    }
  }

  getImageUrl(image: SiteImage, thumb?: string): string {
    if (!image.file) return '';
    let url = `${this.baseUrl}/api/files/site_images/${image.id}/${image.file}`;
    if (thumb) {
      url += `?thumb=${thumb}`;
    }
    return url;
  }
}

// Server content fetcher
export class ServerContentFetcher {
  private static pb = new ServerPocketBase();

  // Get banner content with fallback
  static async getBannerContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('homepage_banner', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          bg_image: data.bg_image || "images/background/hero.webp",
          title: content.title || "We Connect. You Power. The World Runs",
          subtitle: content.subtitle || "Fiber Network Excellence",
          watermark: data.watermark || "PTBL",
          content: content.content || "Empowering businesses and communities with cutting-edge fiber network infrastructure.",
          features: data.features || [
            "99.9% Network Uptime",
            "24/7 Expert Support",
            "Across ECG's operational area"
          ],
          button: data.button || {
            enable: true,
            label: "Contact Us",
            link: "/contact"
          },
          secondary_button: data.secondary_button || {
            enable: true,
            label: "View Services",
            link: "/services"
          }
        };
      }
    } catch (error) {
      console.warn('Failed to fetch banner content from PocketBase, using fallback');
    }

    // Return fallback content
    return this.getFallbackBannerContent(language);
  }

  // Get about content with fallback
  static async getAboutContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('about_section', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          about_item: [{
            image: data.image || "/images/about/framework.png",
            title: content.title || "COMPANY HISTORY",
            content: content.content || "Power Telco Business Limited (PTBL) is ECG's wholly owned subsidiary...",
            button: data.button || {
              enable: true,
              label: "Learn More",
              link: "/about"
            }
          }]
        };
      }
    } catch (error) {
      console.warn('Failed to fetch about content from PocketBase, using fallback');
    }

    return this.getFallbackAboutContent(language);
  }

  // Get features content with fallback
  static async getFeaturesContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('features_section', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          subtitle: content.subtitle || "OUR SERVICES",
          title: content.title || "Comprehensive Network Solutions",
          content: content.content || "powered by ECG's fiber infrastructure",
          feature_item: data.feature_item || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch features content from PocketBase, using fallback');
    }

    return this.getFallbackFeaturesContent(language);
  }

  // Get stats content with fallback
  static async getStatsContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await this.pb.getContentSection('stats_section', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          stats_item: data.stats_item || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch stats content from PocketBase, using fallback');
    }

    return this.getFallbackStatsContent(language);
  }

  // Get image URL with fallback
  static async getImageUrl(imageKey: string, fallbackUrl: string, thumb?: string): Promise<string> {
    try {
      const image = await this.pb.getImage(imageKey);
      if (image) {
        return this.pb.getImageUrl(image, thumb);
      }
    } catch (error) {
      console.warn(`Failed to fetch image ${imageKey} from PocketBase, using fallback`);
    }
    
    return fallbackUrl;
  }

  // Generic content fetcher
  static async getContent(sectionKey: string, language: 'en' | 'fr' = 'en', fallback: Record<string, unknown> = {}) {
    try {
      const content = await this.pb.getContentSection(sectionKey, language);
      
      if (content && content.enabled) {
        return {
          ...fallback,
          ...content.data,
          enable: content.enabled,
          title: content.title,
          subtitle: content.subtitle,
          content: content.content
        };
      }
    } catch (error) {
      console.warn(`Failed to fetch content ${sectionKey} from PocketBase, using fallback`);
    }
    
    return fallback;
  }

  // Fallback content methods
  private static getFallbackBannerContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      bg_image: "images/background/hero.webp",
      title: "Nous Connectons. Vous Alimentez. Le Monde Fonctionne",
      subtitle: "Excellence du Réseau de Fibres",
      watermark: "PTBL",
      content: "Autonomiser les entreprises et les communautés avec une infrastructure de réseau de fibres de pointe.",
      features: [
        "99,9% de Disponibilité du Réseau",
        "Support Expert 24/7",
        "Dans la zone opérationnelle d'ECG"
      ],
      button: {
        enable: true,
        label: "Contactez-nous",
        link: "/fr/contact"
      },
      secondary_button: {
        enable: true,
        label: "Voir les Services",
        link: "/fr/services"
      }
    } : {
      enable: true,
      bg_image: "images/background/hero.webp",
      title: "We Connect. You Power. The World Runs",
      subtitle: "Fiber Network Excellence",
      watermark: "PTBL",
      content: "Empowering businesses and communities with cutting-edge fiber network infrastructure.",
      features: [
        "99.9% Network Uptime",
        "24/7 Expert Support",
        "Across ECG's operational area"
      ],
      button: {
        enable: true,
        label: "Contact Us",
        link: "/contact"
      },
      secondary_button: {
        enable: true,
        label: "View Services",
        link: "/services"
      }
    };
  }

  private static getFallbackAboutContent(language: 'en' | 'fr') {
    return language === 'fr' ? {
      enable: true,
      about_item: [{
        image: "/images/about/framework.png",
        title: "HISTOIRE DE L'ENTREPRISE",
        content: "Power Telco Business Limited (PTBL) est une filiale entièrement détenue d'ECG...",
        button: {
          enable: true,
          label: "En Savoir Plus",
          link: "/fr/about"
        }
      }]
    } : {
      enable: true,
      about_item: [{
        image: "/images/about/framework.png",
        title: "COMPANY HISTORY",
        content: "Power Telco Business Limited (PTBL) is ECG's wholly owned subsidiary...",
        button: {
          enable: true,
          label: "Learn More",
          link: "/about"
        }
      }]
    };
  }

  private static getFallbackFeaturesContent(language: 'en' | 'fr') {
    const defaultFeatures = language === 'fr' ? [
      {
        icon: "fas fa-network-wired",
        title: "Services de Ligne Louée",
        content: "Connectivité dédiée haute vitesse pour les entreprises avec des SLA garantis."
      },
      {
        icon: "fas fa-chart-line",
        title: "Planification de Réseau",
        content: "Solutions de conception de réseau expertes adaptées à vos besoins spécifiques."
      },
      {
        icon: "fas fa-tools",
        title: "Optimisation de Réseau",
        content: "Amélioration continue des performances pour une efficacité maximale."
      }
    ] : [
      {
        icon: "fas fa-network-wired",
        title: "Leased Line Services",
        content: "High-speed dedicated connectivity for businesses with guaranteed SLAs."
      },
      {
        icon: "fas fa-chart-line",
        title: "Network Planning",
        content: "Expert network design solutions tailored to your specific requirements."
      },
      {
        icon: "fas fa-tools",
        title: "Network Optimization",
        content: "Continuous performance enhancement for maximum efficiency."
      }
    ];

    return {
      enable: true,
      subtitle: language === 'fr' ? "NOS SERVICES" : "OUR SERVICES",
      title: language === 'fr' ? "Solutions Réseau Complètes" : "Comprehensive Network Solutions",
      content: language === 'fr' ? "alimentées par l'infrastructure de fibres d'ECG" : "powered by ECG's fiber infrastructure",
      feature_item: defaultFeatures
    };
  }

  private static getFallbackStatsContent(language: 'en' | 'fr') {
    const defaultStats = language === 'fr' ? [
      {
        icon: "fas fa-network-wired",
        number: "500+",
        label: "Kilomètres de Fibre"
      },
      {
        icon: "fas fa-building",
        number: "100+",
        label: "Entreprises Connectées"
      },
      {
        icon: "fas fa-clock",
        number: "99.9%",
        label: "Disponibilité"
      }
    ] : [
      {
        icon: "fas fa-network-wired",
        number: "500+",
        label: "Kilometers of Fiber"
      },
      {
        icon: "fas fa-building",
        number: "100+",
        label: "Connected Businesses"
      },
      {
        icon: "fas fa-clock",
        number: "99.9%",
        label: "Uptime"
      }
    ];

    return {
      enable: true,
      stats_item: defaultStats
    };
  }
}
