import PocketBase from 'pocketbase';

// PocketBase configuration
const PB_URL = import.meta.env.PUBLIC_POCKETBASE_URL || 'http://1********:8090';

// Create PocketBase instance
export const pb = new PocketBase(PB_URL);

// Types for our collections
export interface ContentSection {
  id?: string;
  section_key: string;
  language: 'en' | 'fr';
  title?: string;
  subtitle?: string;
  content?: string;
  data?: Record<string, unknown>;
  enabled?: boolean;
  sort_order?: number;
  created?: string;
  updated?: string;
}

export interface SiteImage {
  id?: string;
  image_key: string;
  alt_text?: string | null;
  file: string;
  description?: string | null;
  category?: string;
  active?: boolean;
  created?: string;
  updated?: string;
}

export interface AdminUser {
  id?: string;
  email: string;
  name: string;
  role: 'admin' | 'editor';
  active?: boolean;
  created?: string;
  updated?: string;
}

// Content management functions
export class ContentManager {
  
  // Get content section by key and language
  static async getContentSection(sectionKey: string, language: 'en' | 'fr' = 'en'): Promise<ContentSection | null> {
    try {
      const record = await pb.collection('content_sections').getFirstListItem(
        `section_key="${sectionKey}" && language="${language}"`
      );
      return record as ContentSection;
    } catch (error) {
      console.warn(`Content section not found: ${sectionKey} (${language})`);
      return null;
    }
  }

  // Get all content sections for a language
  static async getAllContentSections(language: 'en' | 'fr' = 'en'): Promise<ContentSection[]> {
    try {
      const records = await pb.collection('content_sections').getFullList({
        filter: `language="${language}"`,
        sort: 'sort_order,section_key'
      });
      return records as ContentSection[];
    } catch (error) {
      console.error('Error fetching content sections:', error);
      return [];
    }
  }

  // Update or create content section
  static async saveContentSection(data: Partial<ContentSection>): Promise<ContentSection | null> {
    try {
      if (data.id) {
        // Update existing
        const record = await pb.collection('content_sections').update(data.id, data);
        return record as ContentSection;
      } else {
        // Create new
        const record = await pb.collection('content_sections').create(data);
        return record as ContentSection;
      }
    } catch (error) {
      console.error('Error saving content section:', error);
      return null;
    }
  }

  // Get image by key
  static async getImage(imageKey: string): Promise<SiteImage | null> {
    try {
      const record = await pb.collection('site_images').getFirstListItem(
        `image_key="${imageKey}" && active=true`
      );
      return record as SiteImage;
    } catch (error) {
      console.warn(`Image not found: ${imageKey}`);
      return null;
    }
  }

  // Get image URL
  static getImageUrl(image: SiteImage, thumb?: string): string {
    if (!image.file) return '';
    return pb.files.getUrl(image, image.file, { thumb });
  }

  // Upload and save image
  static async saveImage(data: Partial<SiteImage>, file?: File): Promise<SiteImage | null> {
    try {
      const formData = new FormData();
      
      // Add file if provided
      if (file) {
        formData.append('file', file);
      }
      
      // Add other fields
      Object.entries(data).forEach(([key, value]) => {
        if (value !== undefined && key !== 'file') {
          formData.append(key, typeof value === 'object' ? JSON.stringify(value) : String(value));
        }
      });

      if (data.id) {
        // Update existing
        const record = await pb.collection('site_images').update(data.id, formData);
        return record as SiteImage;
      } else {
        // Create new
        const record = await pb.collection('site_images').create(formData);
        return record as SiteImage;
      }
    } catch (error) {
      console.error('Error saving image:', error);
      return null;
    }
  }

  // Get all images by category
  static async getImagesByCategory(category?: string): Promise<SiteImage[]> {
    try {
      const filter = category ? `category="${category}" && active=true` : 'active=true';
      const records = await pb.collection('site_images').getFullList({
        filter,
        sort: '-created'
      });
      return records as SiteImage[];
    } catch (error) {
      console.error('Error fetching images:', error);
      return [];
    }
  }
}

// Authentication functions
export class AuthManager {
  
  // Check if user is authenticated
  static isAuthenticated(): boolean {
    return pb.authStore.isValid;
  }

  // Get current user
  static getCurrentUser(): AdminUser | null {
    if (!pb.authStore.isValid) return null;
    return pb.authStore.model as AdminUser;
  }

  // Login
  static async login(email: string, password: string): Promise<AdminUser | null> {
    try {
      const authData = await pb.collection('admin_users').authWithPassword(email, password);
      return authData.record as AdminUser;
    } catch (error) {
      console.error('Login error:', error);
      return null;
    }
  }

  // Logout
  static logout(): void {
    pb.authStore.clear();
  }

  // Refresh authentication
  static async refresh(): Promise<boolean> {
    try {
      if (pb.authStore.isValid) {
        await pb.collection('admin_users').authRefresh();
        return true;
      }
      return false;
    } catch (error) {
      console.error('Auth refresh error:', error);
      return false;
    }
  }
}

// Initialize PocketBase auth store persistence
if (typeof window !== 'undefined') {
  pb.authStore.onChange(() => {
    // Auto-save auth state to localStorage
    localStorage.setItem('pocketbase_auth', JSON.stringify(pb.authStore));
  });

  // Restore auth state from localStorage
  const savedAuth = localStorage.getItem('pocketbase_auth');
  if (savedAuth) {
    try {
      const authData = JSON.parse(savedAuth);
      pb.authStore.save(authData.token, authData.model);
    } catch (error) {
      console.warn('Failed to restore auth state:', error);
    }
  }
}

export default pb;
