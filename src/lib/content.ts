import { ContentManager } from './pocketbase';

// Content fetching utilities with fallbacks
export class ContentFetcher {
  
  // Get banner content with fallback
  static async getBannerContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('homepage_banner', language);
      
      if (content && content.enabled) {
        // Parse data if it exists
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          bg_image: data.bg_image || "images/background/hero.webp",
          title: content.title || "We Connect. You Power. The World Runs",
          subtitle: content.subtitle || "Fiber Network Excellence",
          watermark: data.watermark || "PTBL",
          content: content.content || "Empowering businesses and communities with cutting-edge fiber network infrastructure.",
          features: data.features || [
            "99.9% Network Uptime",
            "24/7 Expert Support",
            "Across ECG's operational area"
          ],
          button: data.button || {
            enable: true,
            label: "Contact Us",
            link: "/contact"
          },
          secondary_button: data.secondary_button || {
            enable: true,
            label: "View Services",
            link: "/services"
          }
        };
      }
    } catch (error) {
      console.warn('Failed to fetch banner content from PocketBase, using fallback');
    }

    // Fallback content
    return language === 'fr' ? {
      enable: true,
      bg_image: "images/background/hero.webp",
      title: "Nous Connectons. Vous Alimentez. Le Monde Fonctionne",
      subtitle: "Excellence du Réseau de Fibres",
      watermark: "PTBL",
      content: "Autonomiser les entreprises et les communautés avec une infrastructure de réseau de fibres de pointe.",
      features: [
        "99,9% de Disponibilité du Réseau",
        "Support Expert 24/7",
        "Dans la zone opérationnelle d'ECG"
      ],
      button: {
        enable: true,
        label: "Contactez-nous",
        link: "/fr/contact"
      },
      secondary_button: {
        enable: true,
        label: "Voir les Services",
        link: "/fr/services"
      }
    } : {
      enable: true,
      bg_image: "images/background/hero.webp",
      title: "We Connect. You Power. The World Runs",
      subtitle: "Fiber Network Excellence",
      watermark: "PTBL",
      content: "Empowering businesses and communities with cutting-edge fiber network infrastructure.",
      features: [
        "99.9% Network Uptime",
        "24/7 Expert Support",
        "Across ECG's operational area"
      ],
      button: {
        enable: true,
        label: "Contact Us",
        link: "/contact"
      },
      secondary_button: {
        enable: true,
        label: "View Services",
        link: "/services"
      }
    };
  }

  // Get about content with fallback
  static async getAboutContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('about_section', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          about_item: [{
            image: data.image || "/images/about/framework.png",
            title: content.title || "COMPANY HISTORY",
            content: content.content || "Power Telco Business Limited (PTBL) is ECG's wholly owned subsidiary...",
            button: data.button || {
              enable: true,
              label: "Learn More",
              link: "/about"
            }
          }]
        };
      }
    } catch (error) {
      console.warn('Failed to fetch about content from PocketBase, using fallback');
    }

    // Fallback content
    return language === 'fr' ? {
      enable: true,
      about_item: [{
        image: "/images/about/framework.png",
        title: "HISTOIRE DE L'ENTREPRISE",
        content: "Power Telco Business Limited (PTBL) est une filiale entièrement détenue d'ECG...",
        button: {
          enable: true,
          label: "En Savoir Plus",
          link: "/fr/about"
        }
      }]
    } : {
      enable: true,
      about_item: [{
        image: "/images/about/framework.png",
        title: "COMPANY HISTORY",
        content: "Power Telco Business Limited (PTBL) is ECG's wholly owned subsidiary...",
        button: {
          enable: true,
          label: "Learn More",
          link: "/about"
        }
      }]
    };
  }

  // Get features content with fallback
  static async getFeaturesContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('features_section', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          subtitle: content.subtitle || "OUR SERVICES",
          title: content.title || "Comprehensive Network Solutions",
          content: content.content || "powered by ECG's fiber infrastructure",
          feature_item: data.feature_item || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch features content from PocketBase, using fallback');
    }

    // Fallback content
    const defaultFeatures = language === 'fr' ? [
      {
        icon: "fas fa-network-wired",
        title: "Services de Ligne Louée",
        content: "Connectivité dédiée haute vitesse pour les entreprises avec des SLA garantis."
      },
      {
        icon: "fas fa-chart-line",
        title: "Planification de Réseau",
        content: "Solutions de conception de réseau expertes adaptées à vos besoins spécifiques."
      },
      {
        icon: "fas fa-tools",
        title: "Optimisation de Réseau",
        content: "Amélioration continue des performances pour une efficacité maximale."
      }
    ] : [
      {
        icon: "fas fa-network-wired",
        title: "Leased Line Services",
        content: "High-speed dedicated connectivity for businesses with guaranteed SLAs."
      },
      {
        icon: "fas fa-chart-line",
        title: "Network Planning",
        content: "Expert network design solutions tailored to your specific requirements."
      },
      {
        icon: "fas fa-tools",
        title: "Network Optimization",
        content: "Continuous performance enhancement for maximum efficiency."
      }
    ];

    return {
      enable: true,
      subtitle: language === 'fr' ? "NOS SERVICES" : "OUR SERVICES",
      title: language === 'fr' ? "Solutions Réseau Complètes" : "Comprehensive Network Solutions",
      content: language === 'fr' ? "alimentées par l'infrastructure de fibres d'ECG" : "powered by ECG's fiber infrastructure",
      feature_item: defaultFeatures
    };
  }

  // Get team content with fallback
  static async getTeamContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('team_section', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          subtitle: content.subtitle || "OUR TEAM",
          title: content.title || "Meet Our Leadership",
          content: content.content || "Experienced professionals driving PTBL's success",
          team_member: data.team_member || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch team content from PocketBase, using fallback');
    }

    // Fallback content
    return {
      enable: true,
      subtitle: language === 'fr' ? "NOTRE ÉQUIPE" : "OUR TEAM",
      title: language === 'fr' ? "Rencontrez Notre Direction" : "Meet Our Leadership",
      content: language === 'fr' ? "Professionnels expérimentés qui dirigent le succès de PTBL" : "Experienced professionals driving PTBL's success",
      team_member: []
    };
  }

  // Get image URL with fallback
  static async getImageUrl(imageKey: string, fallbackUrl: string, thumb?: string): Promise<string> {
    try {
      const image = await ContentManager.getImage(imageKey);
      if (image) {
        return ContentManager.getImageUrl(image, thumb);
      }
    } catch (error) {
      console.warn(`Failed to fetch image ${imageKey} from PocketBase, using fallback`);
    }
    
    return fallbackUrl;
  }

  // Get stats content with fallback
  static async getStatsContent(language: 'en' | 'fr' = 'en') {
    try {
      const content = await ContentManager.getContentSection('stats_section', language);
      
      if (content && content.enabled) {
        const data = content.data || {};
        
        return {
          enable: content.enabled,
          stats_item: data.stats_item || []
        };
      }
    } catch (error) {
      console.warn('Failed to fetch stats content from PocketBase, using fallback');
    }

    // Fallback content
    const defaultStats = language === 'fr' ? [
      {
        icon: "fas fa-network-wired",
        number: "500+",
        label: "Kilomètres de Fibre"
      },
      {
        icon: "fas fa-building",
        number: "100+",
        label: "Entreprises Connectées"
      },
      {
        icon: "fas fa-clock",
        number: "99.9%",
        label: "Disponibilité"
      }
    ] : [
      {
        icon: "fas fa-network-wired",
        number: "500+",
        label: "Kilometers of Fiber"
      },
      {
        icon: "fas fa-building",
        number: "100+",
        label: "Connected Businesses"
      },
      {
        icon: "fas fa-clock",
        number: "99.9%",
        label: "Uptime"
      }
    ];

    return {
      enable: true,
      stats_item: defaultStats
    };
  }

  // Generic content fetcher
  static async getContent(sectionKey: string, language: 'en' | 'fr' = 'en', fallback: Record<string, unknown> = {}) {
    try {
      const content = await ContentManager.getContentSection(sectionKey, language);
      
      if (content && content.enabled) {
        return {
          ...fallback,
          ...content.data,
          enable: content.enabled,
          title: content.title,
          subtitle: content.subtitle,
          content: content.content
        };
      }
    } catch (error) {
      console.warn(`Failed to fetch content ${sectionKey} from PocketBase, using fallback`);
    }
    
    return fallback;
  }
}
