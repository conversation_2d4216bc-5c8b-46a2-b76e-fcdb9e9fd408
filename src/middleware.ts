import { defineMiddleware } from 'astro:middleware';

// Admin route protection middleware
export const onRequest = defineMiddleware(async (context, next) => {
  const { url, request } = context;
  
  // Check if this is an admin route
  if (url.pathname.startsWith('/edit-ptblgh') || url.pathname.startsWith('/admin')) {
    // Skip authentication check for login page
    if (url.pathname === '/admin/login') {
      return next();
    }

    // For server-side rendering, we can't easily check client-side auth state
    // So we'll let the client-side JavaScript handle the redirect
    // This middleware mainly serves as documentation and future enhancement point
    
    // You could implement server-side session checking here if needed
    // For now, we rely on client-side authentication checks
    
    return next();
  }
  
  // For all other routes, continue normally
  return next();
});

// Alternative approach: You could implement server-side session validation
// by checking cookies or headers for valid authentication tokens

/*
// Example of server-side auth check (commented out for now):
export const onRequest = defineMiddleware(async (context, next) => {
  const { url, request, cookies, redirect } = context;
  
  // Check if this is an admin route
  if (url.pathname.startsWith('/edit-ptblgh') || url.pathname.startsWith('/admin')) {
    // Skip authentication check for login page
    if (url.pathname === '/admin/login') {
      return next();
    }

    // Check for authentication cookie/token
    const authToken = cookies.get('pocketbase_auth')?.value;
    
    if (!authToken) {
      return redirect('/admin/login');
    }

    try {
      // Validate token with PocketBase
      const pbUrl = import.meta.env.PUBLIC_POCKETBASE_URL || 'http://127.0.0.1:8090';
      const response = await fetch(`${pbUrl}/api/collections/admin_users/auth-refresh`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${authToken}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        // Token is invalid, redirect to login
        return redirect('/admin/login');
      }

      // Token is valid, continue
      return next();
    } catch (error) {
      console.error('Auth validation error:', error);
      return redirect('/admin/login');
    }
  }
  
  // For all other routes, continue normally
  return next();
});
*/
