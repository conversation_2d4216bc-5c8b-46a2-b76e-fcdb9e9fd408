# Deployment Guide for PTBL Astro Website

## 🚀 Deployment Options

### Option 1: Railway (Recommended based on user preference)

1. **Connect Repository**
   ```bash
   # Push your code to GitHub
   git add .
   git commit -m "Initial Astro migration"
   git push origin main
   ```

2. **Deploy to Railway**
   - Visit [railway.app](https://railway.app)
   - Connect your GitHub repository
   - Railway will auto-detect Astro and configure build settings
   - Set environment variables if needed

3. **Build Configuration**
   Railway should automatically detect:
   ```json
   {
     "build": "npm run build",
     "start": "npm run preview"
   }
   ```

### Option 2: Netlify

1. **Connect Repository**
   - Visit [netlify.com](https://netlify.com)
   - Click "New site from Git"
   - Connect your GitHub repository

2. **Build Settings**
   ```
   Build command: npm run build
   Publish directory: dist
   ```

3. **Environment Variables**
   ```
   NODE_VERSION=18.20.8
   ```

### Option 3: Vercel

1. **Deploy with Vercel**
   ```bash
   npm i -g vercel
   vercel --prod
   ```

2. **Or via GitHub**
   - Visit [vercel.com](https://vercel.com)
   - Import your GitHub repository
   - Vercel auto-configures Astro projects

## 🔧 Pre-Deployment Checklist

### 1. **Update Node.js Version**
```bash
# Check current version
node --version

# Update to 18.20.8 or higher
# Use nvm (recommended)
nvm install 18.20.8
nvm use 18.20.8
```

### 2. **Test Build Locally**
```bash
# Install dependencies
npm install

# Test build
npm run build

# Test preview
npm run preview
```

### 3. **Verify All Pages**
- [ ] Homepage (English): `/`
- [ ] Homepage (French): `/fr`
- [ ] About page: `/about`
- [ ] About page (French): `/fr/about`
- [ ] Services: `/service`
- [ ] Team: `/team`
- [ ] Pricing: `/pricing`
- [ ] FAQ: `/faq`
- [ ] Contact: `/contact`
- [ ] Contact (French): `/fr/contact`

### 4. **Check Assets**
- [ ] All images load correctly
- [ ] Fonts display properly
- [ ] Icons render correctly
- [ ] CSS styles apply

### 5. **Test Functionality**
- [ ] Navigation works
- [ ] Language switcher functions
- [ ] Mobile menu operates
- [ ] Contact form submits
- [ ] FAQ accordion expands
- [ ] Team modals open
- [ ] Responsive design works

## 🌐 Domain Configuration

### Custom Domain Setup

1. **Add Domain to Hosting Provider**
   - Add your domain in hosting dashboard
   - Configure DNS settings

2. **DNS Configuration**
   ```
   Type: CNAME
   Name: www
   Value: [your-site-url]
   
   Type: A
   Name: @
   Value: [hosting-provider-ip]
   ```

3. **SSL Certificate**
   Most hosting providers auto-provision SSL certificates.

## 📊 Performance Optimization

### 1. **Image Optimization**
```bash
# Install image optimization tools
npm install @astrojs/image

# Add to astro.config.mjs
import image from '@astrojs/image';

export default defineConfig({
  integrations: [tailwind(), sitemap(), image()]
});
```

### 2. **Bundle Analysis**
```bash
# Analyze bundle size
npm run build -- --analyze
```

### 3. **Lighthouse Testing**
- Test with Chrome DevTools
- Aim for 90+ scores in all categories
- Focus on Performance, Accessibility, SEO

## 🔍 SEO Configuration

### 1. **Sitemap**
Already configured in `astro.config.mjs`:
```javascript
integrations: [sitemap()]
```

### 2. **Robots.txt**
Create `public/robots.txt`:
```
User-agent: *
Allow: /

Sitemap: https://ptblgh.com/sitemap-index.xml
```

### 3. **Google Analytics** (Optional)
Add to `BaseLayout.astro`:
```html
<!-- Google Analytics -->
<script async src="https://www.googletagmanager.com/gtag/js?id=GA_MEASUREMENT_ID"></script>
<script>
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());
  gtag('config', 'GA_MEASUREMENT_ID');
</script>
```

## 🛡 Security Headers

### Netlify (_headers file)
Create `public/_headers`:
```
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Content-Security-Policy: default-src 'self'; script-src 'self' 'unsafe-inline' https://unpkg.com https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:;
```

## 📱 Progressive Web App (Optional)

### 1. **Add Manifest**
Create `public/manifest.json`:
```json
{
  "name": "PTBL - Power Telco Business Limited",
  "short_name": "PTBL",
  "description": "Enabling Connectivity",
  "start_url": "/",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#2962ff",
  "icons": [
    {
      "src": "/images/ptbl-logo.png",
      "sizes": "192x192",
      "type": "image/png"
    }
  ]
}
```

### 2. **Add to BaseLayout**
```html
<link rel="manifest" href="/manifest.json">
```

## 🔄 Continuous Deployment

### GitHub Actions (for GitHub Pages)
Create `.github/workflows/deploy.yml`:
```yaml
name: Deploy to GitHub Pages

on:
  push:
    branches: [ main ]

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - uses: actions/setup-node@v2
      with:
        node-version: '18.20.8'
    - run: npm ci
    - run: npm run build
    - uses: peaceiris/actions-gh-pages@v3
      with:
        github_token: ${{ secrets.GITHUB_TOKEN }}
        publish_dir: ./dist
```

## 📈 Monitoring & Analytics

### 1. **Error Monitoring**
Consider adding:
- Sentry for error tracking
- LogRocket for user sessions
- Google Analytics for traffic

### 2. **Performance Monitoring**
- Core Web Vitals tracking
- Lighthouse CI for automated testing
- Real User Monitoring (RUM)

## 🚨 Troubleshooting

### Common Issues

1. **Node.js Version Error**
   ```bash
   # Update Node.js to 18.20.8+
   nvm install 18.20.8
   nvm use 18.20.8
   ```

2. **Build Failures**
   ```bash
   # Clear cache and reinstall
   rm -rf node_modules package-lock.json
   npm install
   npm run build
   ```

3. **Asset Loading Issues**
   - Check file paths in `public/` directory
   - Verify image references in components
   - Ensure proper case sensitivity

4. **Styling Issues**
   - Verify Tailwind CSS is properly configured
   - Check for conflicting CSS rules
   - Test responsive breakpoints

## 📞 Support

For deployment issues:
- Check hosting provider documentation
- Review build logs for errors
- Test locally before deploying
- Contact hosting support if needed

## ✅ Post-Deployment Checklist

- [ ] Site loads correctly
- [ ] All pages accessible
- [ ] Images display properly
- [ ] Forms work correctly
- [ ] Mobile responsive
- [ ] Language switcher functions
- [ ] SEO tags present
- [ ] SSL certificate active
- [ ] Custom domain configured
- [ ] Analytics tracking (if added)

---

**The PTBL Astro website is now ready for production deployment! 🚀**
